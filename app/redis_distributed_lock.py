# coding: utf8
"""
Redis Distributed Lock Implementation
Optimized cho high-throughput production environment
"""

import os
import random
import threading
import time
import uuid
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from threading import Lock as ThreadLock

from loguru import logger
import redis



class MemoryManagedMetrics:
    """
    Thread-safe metrics collector với memory management
    Prevents memory leaks trong long-running systems
    """
    def __init__(self, max_entries=1000, ttl_hours=1):
        self._lock = ThreadLock()
        self._metrics = {}
        self._timestamps = {}
        self.max_entries = max_entries
        self.ttl_seconds = ttl_hours * 3600
        self._last_cleanup = time.time()
        self._shutdown_event = threading.Event()

        # Start cleanup thread với proper shutdown handling
        self._cleanup_thread = threading.Thread(
            target=self._periodic_cleanup,
            daemon=False,  # Changed to False để có control
            name="MetricsCleanup"
        )
        self._cleanup_thread.start()

        # Register shutdown handler
        import atexit
        atexit.register(self._shutdown_cleanup)

    def _get_default_metrics(self):
        """Get default metrics structure"""
        return {
            'acquire_count': 0,
            'acquire_success': 0,
            'acquire_timeout': 0,
            'acquire_error': 0,
            'total_acquire_time': 0.0,
            'max_acquire_time': 0.0,
            'release_count': 0,
            'release_success': 0,
            'release_error': 0,
            'extend_count': 0,
            'extend_success': 0
        }

    def _periodic_cleanup(self):
        """Periodic cleanup với graceful shutdown"""
        logger.debug("Started metrics cleanup thread")

        while not self._shutdown_event.is_set():
            try:
                # Wait với timeout thay vì sleep để có thể interrupt
                if self._shutdown_event.wait(timeout=300):  # 5 phút hoặc shutdown signal
                    break

                self._cleanup_expired()
                self._enforce_size_limit()

            except Exception as e:
                logger.error(f"Error in metrics cleanup: {e}")
                # Continue running despite errors

        # Final cleanup trước khi exit
        try:
            logger.debug("Performing final metrics cleanup")
            self._cleanup_expired()
            self._enforce_size_limit()
        except Exception as e:
            logger.error(f"Error in final cleanup: {e}")

        logger.debug("Metrics cleanup thread stopped")

    def _shutdown_cleanup(self):
        """Graceful shutdown cho cleanup thread"""
        logger.debug("Shutting down metrics cleanup thread")
        self._shutdown_event.set()

        if self._cleanup_thread and self._cleanup_thread.is_alive():
            # Wait for thread to finish gracefully
            self._cleanup_thread.join(timeout=5.0)
            if self._cleanup_thread.is_alive():
                logger.warning("Metrics cleanup thread did not stop gracefully")
            else:
                logger.debug("Metrics cleanup thread stopped gracefully")

    def _cleanup_expired(self):
        """Remove expired entries based on TTL"""
        current_time = time.time()
        with self._lock:
            expired_keys = [
                key for key, timestamp in self._timestamps.items()
                if current_time - timestamp > self.ttl_seconds
            ]
            for key in expired_keys:
                self._metrics.pop(key, None)
                self._timestamps.pop(key, None)

            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired metrics entries")

    def _enforce_size_limit(self):
        """Enforce max entries limit by removing oldest"""
        with self._lock:
            if len(self._metrics) > self.max_entries:
                # Sort by timestamp, remove oldest
                sorted_items = sorted(
                    self._timestamps.items(),
                    key=lambda x: x[1]
                )
                to_remove = len(self._metrics) - self.max_entries
                removed_keys = []

                for key, _ in sorted_items[:to_remove]:
                    self._metrics.pop(key, None)
                    self._timestamps.pop(key, None)
                    removed_keys.append(key)

                if removed_keys:
                    logger.debug(f"Removed {len(removed_keys)} metrics entries due to size limit")

    def _ensure_entry_exists(self, lock_key):
        """Ensure metrics entry exists for lock_key"""
        if lock_key not in self._metrics:
            self._metrics[lock_key] = self._get_default_metrics()
            self._timestamps[lock_key] = time.time()


class LockMetrics(MemoryManagedMetrics):
    """
    Backward compatible wrapper cho MemoryManagedMetrics
    """
    def __init__(self, max_entries=1000, ttl_hours=1):
        super().__init__(max_entries, ttl_hours)

    def record_acquire_attempt(self, lock_key, success, duration, error=None):
        with self._lock:
            self._ensure_entry_exists(lock_key)
            metrics = self._metrics[lock_key]
            metrics['acquire_count'] += 1
            metrics['total_acquire_time'] += duration
            metrics['max_acquire_time'] = max(metrics['max_acquire_time'], duration)

            if success:
                metrics['acquire_success'] += 1
            elif error:
                metrics['acquire_error'] += 1
            else:
                metrics['acquire_timeout'] += 1

            # Update timestamp
            self._timestamps[lock_key] = time.time()

    def record_release_attempt(self, lock_key, success):
        with self._lock:
            self._ensure_entry_exists(lock_key)
            metrics = self._metrics[lock_key]
            metrics['release_count'] += 1
            if success:
                metrics['release_success'] += 1
            else:
                metrics['release_error'] += 1

            # Update timestamp
            self._timestamps[lock_key] = time.time()

    def record_extend_attempt(self, lock_key, success):
        with self._lock:
            self._ensure_entry_exists(lock_key)
            metrics = self._metrics[lock_key]
            metrics['extend_count'] += 1
            if success:
                metrics['extend_success'] += 1

            # Update timestamp
            self._timestamps[lock_key] = time.time()

    def get_metrics(self, lock_key=None):
        with self._lock:
            if lock_key:
                self._ensure_entry_exists(lock_key)
                return dict(self._metrics[lock_key])
            return {k: dict(v) for k, v in self._metrics.items()}

    def get_memory_stats(self):
        """Get memory usage statistics"""
        with self._lock:
            return {
                'total_entries': len(self._metrics),
                'max_entries': self.max_entries,
                'memory_usage_pct': len(self._metrics) / self.max_entries * 100,
                'ttl_seconds': self.ttl_seconds,
                'oldest_entry_age': time.time() - min(self._timestamps.values()) if self._timestamps else 0
            }

    def should_log_summary(self, lock_key, config):
        """
        Determine nếu nên log summary dựa trên config và metrics
        """
        if not config.log_only_issues:
            return random.random() < config.log_sampling_rate

        metrics = self.get_metrics(lock_key)
        if metrics['acquire_count'] == 0:
            return False

        # Log nếu có issues
        has_issues = (
            metrics['acquire_timeout'] > 0 or
            metrics['acquire_error'] > 0 or
            metrics['max_acquire_time'] > config.log_performance_threshold
        )

        return has_issues or (random.random() < config.log_sampling_rate)

    def log_summary(self, lock_key, config=None):
        """
        Log summary với conditional logging và sampling
        """
        if config and not self.should_log_summary(lock_key, config):
            return

        metrics = self.get_metrics(lock_key)
        if metrics['acquire_count'] > 0:
            avg_time = metrics['total_acquire_time'] / metrics['acquire_count']
            success_rate = metrics['acquire_success'] / metrics['acquire_count'] * 100

            # Determine log level based on performance
            if metrics['acquire_error'] > 0 or success_rate < 90:
                log_level = "error"
            elif metrics['max_acquire_time'] > (config.log_performance_threshold if config else 1.0):
                log_level = "warning"
            else:
                log_level = "info"

            message = (f"Lock metrics for {lock_key}: "
                      f"attempts={metrics['acquire_count']}, "
                      f"success_rate={success_rate:.1f}%, "
                      f"avg_time={avg_time:.3f}s, "
                      f"max_time={metrics['max_acquire_time']:.3f}s")

            if log_level == "error":
                logger.error(message)
            elif log_level == "warning":
                logger.warning(message)
            else:
                logger.info(message)


# Global metrics instance với memory management
lock_metrics = LockMetrics(max_entries=1000, ttl_hours=1)



class RedisDistributedLock:
    """
    Redis distributed-lock với high-performance optimization
    Optimized cho 200 concurrent requests, API calls 3-5s
    """

    def __init__(self, redis_client, username=None, config=None):
        self.username = username or "default"
        self.config = config or LockConfig()

        # Wrap Redis client với resilient wrapper
        self.redis = ResilientRedisWrapper(redis_client, self.config)

        # Secure lock key với namespace và hash
        username_hash = hash(self.username) % 10000
        self.lock_key = f"redis-distributed:lock:token:{username_hash}:{self.username}"

        # Enhanced rate limit key với unique process ID
        if self.config.per_process_rate_limit:
            if self.config.use_unique_process_id:
                process_id = ProcessIdentifier.get_identifier()
            else:
                process_id = os.getpid()
            self.rate_limit_key = f"redis-distributed:rate_limit:lock:{username_hash}:{self.username}:{process_id}"
        else:
            self.rate_limit_key = f"redis-distributed:rate_limit:lock:{username_hash}:{self.username}"

        self.lock_timeout = self.config.lock_timeout * 1000  # Convert to milliseconds
        self.acquire_timeout = self.config.acquire_timeout
        self.owner_id = None

        # Thread management - shared pool vs individual
        if self.config.use_shared_thread_pool:
            self._auto_extend_pool = SharedAutoExtendPool.get_instance()
            self._auto_extend_future = None
        else:
            # Fallback to individual thread (legacy)
            self._extend_thread = None

        self._stop_extend = False

        # Lua script để release an toàn
        self.release_script = """
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('DEL', KEYS[1])
        else
            return 0
        end
        """

        # Lua script để extend lock
        self.extend_script = """
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('PEXPIRE', KEYS[1], ARGV[2])
        else
            return 0
        end
        """

    def _check_rate_limit(self):
        """
        Check rate limiting cho acquire attempts - optimized cho high throughput
        Returns: True nếu trong limit, False nếu vượt quá
        """
        try:
            current_count = self.redis.incr(self.rate_limit_key)
            if current_count == 1:
                # Set TTL cho key mới
                self.redis.expire(self.rate_limit_key, self.config.rate_limit_window)

            if current_count > self.config.max_rate_limit:
                logger.warning(f"Rate limit exceeded for {self.username} (process {os.getpid()}): "
                             f"{current_count}/{self.config.max_rate_limit} attempts")
                return False
            return True
        except redis.RedisError as e:
            logger.error(f"Error checking rate limit: {e}")
            # Cho phép tiếp tục nếu Redis error
            return True

    def acquire_with_retry(self):
        """
        Acquire lock với retry mechanism cho rate limiting
        Returns: True nếu thành công, False nếu failed sau retries
        """
        if not self.config.enable_retry:
            return self.acquire()

        for retry_attempt in range(self.config.max_retries + 1):
            result = self.acquire()
            if result:
                return True

            # Retry với exponential backoff nếu bị rate limited
            if retry_attempt < self.config.max_retries:
                sleep_time = min(
                    self.config.retry_backoff_base * (2 ** retry_attempt),
                    self.config.retry_backoff_max
                )
                logger.info(f"Retrying acquire after {sleep_time:.2f}s (attempt {retry_attempt + 1})")
                time.sleep(sleep_time)

        logger.error(f"Failed to acquire lock after {self.config.max_retries} retries")
        return False

    def acquire(self):
        """
        Chiếm lock với optimized backoff cho high throughput
        Returns: True nếu thành công, False nếu timeout hoặc rate limited
        """
        # Check rate limiting trước
        if not self._check_rate_limit():
            return False

        self.owner_id = str(uuid.uuid4())
        start_time = time.time()
        attempt = 0
        success = False
        error = None

        try:
            # Thử ngay lập tức
            if self.redis.set(self.lock_key, self.owner_id, nx=True, px=self.lock_timeout):
                success = True
                logger.debug(f"Lock acquired immediately: {self.lock_key}")
                self._start_auto_extend()
                return True

            # Poll với optimized exponential backoff
            while (time.time() - start_time) < self.acquire_timeout:
                # Optimized backoff: faster convergence, lower max delay
                sleep_time = min(
                    self.config.base_backoff * (2 ** attempt),
                    self.config.max_backoff
                )
                jitter = random.uniform(0, self.config.jitter_range)
                time.sleep(sleep_time + jitter)

                if self.redis.set(self.lock_key, self.owner_id, nx=True, px=self.lock_timeout):
                    success = True
                    acquire_time = time.time() - start_time
                    logger.info(f"Lock acquired after {acquire_time:.3f}s, attempts: {attempt + 1}")
                    self._start_auto_extend()
                    return True

                attempt += 1

            # Timeout
            logger.warning(f"Lock acquire timeout after {self.acquire_timeout}s, attempts: {attempt + 1}")
            return False

        except redis.RedisError as e:
            error = e
            logger.error(f"Redis error during lock acquire: {e}")
            return False
        except Exception as e:
            error = e
            logger.exception(f"Unexpected error during lock acquire: {e}")
            return False
        finally:
            # Record metrics
            duration = time.time() - start_time
            lock_metrics.record_acquire_attempt(self.lock_key, success, duration, error)

    def release(self):
        """
        Release lock an toàn với cleanup auto-extend thread
        """
        if not self.owner_id:
            logger.warning("Attempting to release lock without owner_id")
            lock_metrics.record_release_attempt(self.lock_key, False)
            return False

        # Stop auto-extend thread trước
        self._stop_auto_extend()

        success = False
        try:
            result = self.redis.eval(
                self.release_script,
                1,
                self.lock_key,
                self.owner_id
            )

            if result == 1:
                success = True
                logger.debug(f"Lock released successfully: {self.lock_key}")
                self.owner_id = None
                return True
            else:
                logger.warning(f"Failed to release lock (not owner): {self.lock_key}")
                return False

        except redis.RedisError as e:
            logger.error(f"Redis error during lock release: {e}")
            return False
        except Exception as e:
            logger.exception(f"Unexpected error during lock release: {e}")
            return False
        finally:
            lock_metrics.record_release_attempt(self.lock_key, success)

    def extend_lock(self, additional_time_ms=5000):
        """
        Extend lock nếu vẫn được sở hữu bởi instance này
        Returns: True nếu thành công, False nếu không sở hữu lock
        """
        if not self.owner_id:
            lock_metrics.record_extend_attempt(self.lock_key, False)
            return False

        success = False
        try:
            result = self.redis.eval(
                self.extend_script,
                1,
                self.lock_key,
                self.owner_id,
                additional_time_ms
            )

            if result == 1:
                success = True
                logger.debug(f"Lock extended by {additional_time_ms}ms: {self.lock_key}")
                return True
            else:
                logger.warning(f"Failed to extend lock (not owner): {self.lock_key}")
                return False

        except redis.RedisError as e:
            logger.error(f"Redis error during lock extend: {e}")
            return False
        except Exception as e:
            logger.exception(f"Unexpected error during lock extend: {e}")
            return False
        finally:
            lock_metrics.record_extend_attempt(self.lock_key, success)

    def is_locked(self):
        """
        Check nếu lock hiện tại đang được giữ
        """
        try:
            return self.redis.exists(self.lock_key) == 1
        except redis.RedisError as e:
            logger.error(f"Redis error checking lock status: {e}")
            return False

    def get_lock_owner(self):
        """
        Lấy owner hiện tại của lock
        """
        try:
            owner = self.redis.get(self.lock_key)
            return owner.decode('utf-8') if owner else None
        except redis.RedisError as e:
            logger.error(f"Redis error getting lock owner: {e}")
            return None

    def get_lock_ttl(self):
        """
        Lấy TTL còn lại của lock (milliseconds)
        """
        try:
            ttl = self.redis.pttl(self.lock_key)
            return ttl if ttl > 0 else 0
        except redis.RedisError as e:
            logger.error(f"Redis error getting lock TTL: {e}")
            return 0

    def _start_auto_extend(self):
        """
        Start auto-extend using shared thread pool hoặc individual thread
        """
        if self.config.auto_extend_interval <= 0:
            return

        if self.config.use_shared_thread_pool:
            # Use shared thread pool
            self._auto_extend_future = self._auto_extend_pool.submit_auto_extend(self)
            if self._auto_extend_future:
                logger.debug(f"Started shared auto-extend for {self.lock_key}")
            else:
                logger.warning(f"Failed to start shared auto-extend for {self.lock_key}")
        else:
            # Fallback to individual thread (legacy)
            self._start_individual_auto_extend()

    def _start_individual_auto_extend(self):
        """
        Fallback: Start individual thread cho auto-extend (legacy mode)
        """
        if not hasattr(self, '_extend_thread') or not self._extend_thread:
            self._stop_extend = False
            self._extend_thread = threading.Thread(
                target=self._individual_auto_extend_worker,
                daemon=True,
                name=f"RedisDistributedLockExtend-{self.username}"
            )
            self._extend_thread.start()
            logger.debug(f"Started individual auto-extend thread for {self.lock_key}")

    def _individual_auto_extend_worker(self):
        """
        Individual thread worker cho auto-extend với backoff on failures
        """
        consecutive_failures = 0
        max_failures = 5
        base_backoff = 1.0  # 1 second base backoff

        while self.owner_id and not self._stop_extend:
            try:
                # Dynamic sleep interval based on failures
                if consecutive_failures > 0:
                    # Exponential backoff với jitter
                    backoff = min(base_backoff * (2 ** consecutive_failures), 30.0)
                    jitter = random.uniform(0, backoff * 0.1)
                    sleep_time = backoff + jitter
                    logger.debug(f"Auto-extend backoff: {sleep_time:.2f}s after {consecutive_failures} failures")
                else:
                    sleep_time = self.config.auto_extend_interval

                time.sleep(sleep_time)

                if self.owner_id and not self._stop_extend:
                    current_ttl = self.get_lock_ttl()
                    if 0 < current_ttl < self.config.auto_extend_threshold_ms:
                        logger.info(f"Auto-extending lock {self.lock_key}, TTL: {current_ttl}ms")

                        # Try to extend lock
                        extend_success = self.extend_lock(self.config.auto_extend_amount_ms)

                        if extend_success:
                            consecutive_failures = 0  # Reset on success
                        else:
                            consecutive_failures += 1
                            logger.warning(f"Auto-extend failed (attempt {consecutive_failures})")

                            # Stop if too many failures
                            if consecutive_failures >= max_failures:
                                logger.error(f"Auto-extend stopping after {max_failures} consecutive failures")
                                break

            except Exception as e:
                consecutive_failures += 1
                logger.error(f"Error in individual auto-extend worker (failure {consecutive_failures}): {e}")

                if consecutive_failures >= max_failures:
                    logger.error(f"Auto-extend worker stopping after {max_failures} consecutive errors")
                    break

        logger.debug(f"Individual auto-extend thread stopped for {self.lock_key}")

    def _stop_auto_extend(self):
        """
        Stop auto-extend với proper cleanup cho both modes
        """
        self._stop_extend = True

        if self.config.use_shared_thread_pool:
            # Cancel shared pool task
            if hasattr(self, '_auto_extend_future') and self._auto_extend_future:
                cancelled = self._auto_extend_pool.cancel_auto_extend(self)
                if cancelled:
                    logger.debug(f"Cancelled shared auto-extend for {self.lock_key}")
                self._auto_extend_future = None
        else:
            # Stop individual thread
            if hasattr(self, '_extend_thread') and self._extend_thread and self._extend_thread.is_alive():
                # Give thread time to stop gracefully
                self._extend_thread.join(timeout=1.0)
                if self._extend_thread.is_alive():
                    logger.warning(f"Auto-extend thread did not stop gracefully for {self.lock_key}")
            self._extend_thread = None

    def auto_extend_if_needed(self, min_ttl_ms=None, extend_by_ms=None):
        """
        Manual auto-extend (backward compatibility)
        """
        min_ttl = min_ttl_ms or self.config.auto_extend_threshold_ms
        extend_by = extend_by_ms or self.config.auto_extend_amount_ms

        try:
            current_ttl = self.get_lock_ttl()
            if 0 < current_ttl < min_ttl:
                logger.info(f"Manual auto-extending lock {self.lock_key}, TTL: {current_ttl}ms")
                return self.extend_lock(extend_by)
            return True  # Không cần extend hoặc lock đã hết hạn
        except Exception as e:
            logger.error(f"Error in auto_extend_if_needed: {e}")
            return False

    def __enter__(self):
        # Sử dụng acquire_with_retry nếu enabled
        acquire_method = self.acquire_with_retry if self.config.enable_retry else self.acquire
        if not acquire_method():
            raise Exception(f"Không thể chiếm lock sau {self.acquire_timeout}s")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()
        # Optimized logging với conditional + sampling
        lock_metrics.log_summary(self.lock_key, self.config)
        # Log nếu có exception trong context
        if exc_type:
            logger.error(f"Exception in lock context: {exc_type.__name__}: {exc_val}")



class ProcessIdentifier:
    """
    Generate unique process identifier để tránh collision
    """
    _identifier = None
    _lock = threading.Lock()

    @classmethod
    def get_identifier(cls):
        if cls._identifier is None:
            with cls._lock:
                if cls._identifier is None:
                    import uuid
                    pid = os.getpid()
                    timestamp = int(time.time())
                    uuid_short = str(uuid.uuid4())[:8]
                    cls._identifier = f"{pid}_{timestamp}_{uuid_short}"
        return cls._identifier


class SharedAutoExtendPool:
    """
    Global thread pool cho auto-extend operations
    Giảm từ hàng trăm threads xuống 5 threads cho toàn bộ application
    """
    _instance = None
    _lock = threading.Lock()

    def __init__(self):
        self._pool = ThreadPoolExecutor(
            max_workers=5,  # Chỉ 5 threads cho tất cả locks
            thread_name_prefix="RedisDistributedLockAutoExtend"
        )
        self._active_tasks = {}  # lock_key -> Future
        self._shutdown = False

        # Register shutdown handlers
        import atexit
        import signal
        atexit.register(self.shutdown)

        # Handle Docker/Kubernetes signals
        try:
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
        except (ValueError, OSError):
            # Ignore if signals can't be set (e.g., in threads)
            pass

    def _signal_handler(self, signum, _):
        """
        Handle shutdown signals từ Docker/Kubernetes
        """
        logger.info(f"Received signal {signum}, initiating graceful shutdown")
        self.shutdown()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def submit_auto_extend(self, lock_instance):
        """Submit auto-extend task cho lock instance"""
        if self._shutdown or lock_instance.lock_key in self._active_tasks:
            return None

        future = self._pool.submit(self._auto_extend_worker, lock_instance)
        self._active_tasks[lock_instance.lock_key] = future

        # Cleanup khi task complete
        def cleanup_task(_):
            self._active_tasks.pop(lock_instance.lock_key, None)
        future.add_done_callback(cleanup_task)

        return future

    def cancel_auto_extend(self, lock_instance):
        """Cancel auto-extend task cho lock instance"""
        if lock_instance.lock_key in self._active_tasks:
            future = self._active_tasks.pop(lock_instance.lock_key)
            future.cancel()
            return True
        return False

    def _auto_extend_worker(self, lock_instance):
        """Worker function cho auto-extend với intelligent backoff"""
        consecutive_failures = 0
        max_failures = 5
        base_backoff = 1.0  # 1 second base backoff

        try:
            while (lock_instance.owner_id and
                   not lock_instance._stop_extend and
                   not self._shutdown):

                # Dynamic sleep interval based on failures
                if consecutive_failures > 0:
                    # Exponential backoff với jitter để reduce CPU spike
                    backoff = min(base_backoff * (2 ** consecutive_failures), 30.0)
                    jitter = random.uniform(0, backoff * 0.1)
                    sleep_time = backoff + jitter
                    logger.debug(f"Shared auto-extend backoff: {sleep_time:.2f}s after {consecutive_failures} failures")
                else:
                    sleep_time = lock_instance.config.auto_extend_interval

                time.sleep(sleep_time)

                if (lock_instance.owner_id and
                    not lock_instance._stop_extend and
                    not self._shutdown):

                    try:
                        current_ttl = lock_instance.get_lock_ttl()
                        if 0 < current_ttl < lock_instance.config.auto_extend_threshold_ms:
                            logger.info(f"Auto-extending lock {lock_instance.lock_key}, TTL: {current_ttl}ms")

                            # Try to extend lock
                            extend_success = lock_instance.extend_lock(lock_instance.config.auto_extend_amount_ms)

                            if extend_success:
                                consecutive_failures = 0  # Reset on success
                            else:
                                consecutive_failures += 1
                                logger.warning(f"Shared auto-extend failed for {lock_instance.lock_key} (attempt {consecutive_failures})")

                                # Stop if too many failures
                                if consecutive_failures >= max_failures:
                                    logger.error(f"Shared auto-extend stopping for {lock_instance.lock_key} after {max_failures} failures")
                                    break

                    except Exception as extend_error:
                        consecutive_failures += 1
                        logger.error(f"Error extending lock {lock_instance.lock_key} (failure {consecutive_failures}): {extend_error}")

                        if consecutive_failures >= max_failures:
                            logger.error(f"Shared auto-extend worker stopping for {lock_instance.lock_key} after {max_failures} errors")
                            break

        except Exception as e:
            logger.error(f"Error in shared auto-extend worker for {lock_instance.lock_key}: {e}")
        finally:
            # Cleanup
            self._active_tasks.pop(lock_instance.lock_key, None)
            logger.debug(f"Shared auto-extend worker stopped for {lock_instance.lock_key}")

    def shutdown(self):
        """Graceful shutdown thread pool"""
        if self._shutdown:
            return  # Already shutdown

        logger.debug("Shutting down SharedAutoExtendPool")
        self._shutdown = True

        # Cancel all active tasks
        cancelled_count = 0
        for future in list(self._active_tasks.values()):
            if future.cancel():
                cancelled_count += 1

        logger.debug(f"Cancelled {cancelled_count} auto-extend tasks")
        self._active_tasks.clear()

        # Enhanced shutdown với aggressive timeout handling
        try:
            # Start shutdown immediately
            self._pool.shutdown(wait=False)

            # Aggressive timeout với multiple phases
            shutdown_start = time.time()
            phase1_timeout = 3  # Phase 1: Quick check (3s)
            phase2_timeout = 7  # Phase 2: Extended wait (total 10s)

            # Phase 1: Quick completion check
            while time.time() - shutdown_start < phase1_timeout:
                active_count = sum(1 for f in self._active_tasks.values() if not f.done())
                if active_count == 0:
                    logger.debug("All tasks completed in phase 1")
                    break
                time.sleep(0.05)  # Check every 50ms for faster response

            # Phase 2: Extended wait với progress monitoring
            if time.time() - shutdown_start < phase2_timeout:
                last_active_count = len(self._active_tasks)
                stall_count = 0

                while time.time() - shutdown_start < phase2_timeout:
                    active_count = sum(1 for f in self._active_tasks.values() if not f.done())

                    if active_count == 0:
                        logger.debug("All tasks completed in phase 2")
                        break

                    # Detect stalled tasks
                    if active_count == last_active_count:
                        stall_count += 1
                        if stall_count > 10:  # 1 second of no progress
                            logger.warning(f"Tasks appear stalled, forcing cancellation")
                            for future in self._active_tasks.values():
                                future.cancel()
                            break
                    else:
                        stall_count = 0

                    last_active_count = active_count
                    time.sleep(0.1)

            # Final cleanup
            elapsed = time.time() - shutdown_start
            if elapsed < phase2_timeout:
                logger.debug(f"SharedAutoExtendPool shutdown completed in {elapsed:.2f}s")
            else:
                logger.warning(f"SharedAutoExtendPool shutdown timeout after {elapsed:.2f}s")
                # Force cancel any remaining tasks
                for future in list(self._active_tasks.values()):
                    future.cancel()

        except Exception as e:
            logger.error(f"Error during pool shutdown: {e}")
            # Emergency cleanup
            for future in list(self._active_tasks.values()):
                try:
                    future.cancel()
                except:
                    pass

    def force_shutdown(self, timeout=5):
        """
        Force shutdown với progressive timeout cho emergency cases
        """
        if self._shutdown:
            return

        logger.warning("Force shutdown initiated")
        self._shutdown = True

        start_time = time.time()

        try:
            # Phase 1: Graceful cancellation (50% of timeout)
            grace_timeout = timeout * 0.5
            logger.debug("Phase 1: Graceful task cancellation")

            for future in list(self._active_tasks.values()):
                future.cancel()

            # Wait for graceful completion
            while time.time() - start_time < grace_timeout:
                active_count = sum(1 for f in self._active_tasks.values() if not f.done())
                if active_count == 0:
                    logger.debug("All tasks cancelled gracefully")
                    break
                time.sleep(0.1)

            # Phase 2: Pool shutdown (remaining timeout)
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time > 0:
                logger.debug(f"Phase 2: Pool shutdown ({remaining_time:.1f}s remaining)")
                self._pool.shutdown(wait=False)

                # Brief wait for pool cleanup
                time.sleep(min(remaining_time, 1.0))

            # Clear remaining tasks
            self._active_tasks.clear()

            elapsed = time.time() - start_time
            logger.warning(f"Force shutdown completed in {elapsed:.2f}s")

        except Exception as e:
            logger.error(f"Error during force shutdown: {e}")
            # Emergency cleanup
            try:
                self._active_tasks.clear()
                self._pool.shutdown(wait=False)
            except:
                pass

    def __del__(self):
        """Destructor với careful cleanup"""
        try:
            if not self._shutdown:
                # Use normal shutdown first, then force if needed
                logger.debug("Destructor: attempting graceful shutdown")

                # Quick graceful attempt
                self._shutdown = True
                for future in list(self._active_tasks.values()):
                    future.cancel()

                # Brief wait for cancellation
                start_time = time.time()
                while time.time() - start_time < 1.0:  # 1 second max
                    active_count = sum(1 for f in self._active_tasks.values() if not f.done())
                    if active_count == 0:
                        break
                    time.sleep(0.1)

                # Final cleanup
                self._active_tasks.clear()
                try:
                    self._pool.shutdown(wait=False)
                except:
                    pass

                logger.debug("Destructor cleanup completed")
        except Exception:
            # Ignore all errors during destruction
            pass


@dataclass
class LockConfig:
    """
    Configuration cho Redis Distributed Lock - production optimized
    """
    # Core timeouts - optimized cho API calls 3-5s
    lock_timeout: int = 7  # seconds (was 10) - API 3-5s + 2s buffer
    acquire_timeout: float = 1.5  # seconds (was 2) - faster for high throughput

    # Rate limiting - optimized cho 200 concurrent requests
    max_rate_limit: int = 300  # per minute (was 60)
    rate_limit_window: int = 60  # seconds
    per_process_rate_limit: bool = True  # Separate limits per process
    use_unique_process_id: bool = True  # Use enhanced process ID

    # Backoff strategy - optimized cho low latency
    base_backoff: float = 0.03  # seconds (was 0.05) - even faster
    max_backoff: float = 0.15  # seconds (was 0.2) - lower max
    jitter_range: float = 0.03  # seconds

    # Auto-extend settings - more aggressive
    auto_extend_interval: int = 2  # seconds (was 3) - more frequent
    auto_extend_threshold_ms: int = 3000  # 3s (was 5s) - earlier trigger
    auto_extend_amount_ms: int = 8000  # 8s (was 10s) - smaller extension

    # Retry mechanism
    enable_retry: bool = True
    max_retries: int = 3
    retry_backoff_base: float = 0.5  # seconds
    retry_backoff_max: float = 2.0  # seconds

    # Logging optimization
    log_sampling_rate: float = 0.1  # 10% sampling
    log_only_issues: bool = True  # Only log timeouts/errors
    log_performance_threshold: float = 1.0  # Log if acquire > 1s

    # Thread management - NEW
    use_shared_thread_pool: bool = True  # Use shared pool instead of individual threads
    max_auto_extend_threads: int = 5  # Max threads in shared pool

    # Memory management - NEW
    metrics_max_entries: int = 1000  # Max metrics entries
    metrics_ttl_hours: int = 1  # TTL for metrics cleanup

    # Redis fallback mechanisms - NEW
    enable_redis_fallback: bool = True  # Enable fallback when Redis fails
    redis_retry_attempts: int = 3  # Retry attempts for Redis operations
    redis_retry_backoff: float = 0.1  # Base backoff for Redis retries
    redis_timeout: float = 5.0  # Timeout for Redis operations
    fallback_to_local: bool = False  # Fallback to local lock (not distributed)
    circuit_breaker_threshold: int = 5  # Failures before circuit breaker opens
    circuit_breaker_timeout: int = 30  # Seconds before circuit breaker resets


class RedisCircuitBreaker:
    """
    Circuit breaker cho Redis operations để prevent cascade failures
    """

    def __init__(self, failure_threshold=5, timeout=30):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self._lock = threading.Lock()

    def call(self, func, *args, **kwargs):
        """Execute function với circuit breaker protection"""
        with self._lock:
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.timeout:
                    self.state = "HALF_OPEN"
                    logger.info("Circuit breaker transitioning to HALF_OPEN")
                else:
                    raise Exception("Circuit breaker is OPEN")

            try:
                result = func(*args, **kwargs)

                if self.state == "HALF_OPEN":
                    self.state = "CLOSED"
                    self.failure_count = 0
                    logger.info("Circuit breaker reset to CLOSED")

                return result

            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()

                if self.failure_count >= self.failure_threshold:
                    self.state = "OPEN"
                    logger.warning(f"Circuit breaker opened after {self.failure_count} failures")

                raise e


class ResilientRedisWrapper:
    """
    Wrapper cho Redis client với retry, timeout, và circuit breaker
    """

    def __init__(self, redis_client, config):
        self.redis = redis_client
        self.config = config
        self.circuit_breaker = RedisCircuitBreaker(
            failure_threshold=config.circuit_breaker_threshold,
            timeout=config.circuit_breaker_timeout
        )
        self._local_locks = {}  # Fallback local locks
        self._local_lock = threading.Lock()

    def _execute_with_retry(self, operation_name, func, *args, **kwargs):
        """Execute Redis operation với retry và circuit breaker"""
        if not self.config.enable_redis_fallback:
            return func(*args, **kwargs)

        last_exception = None

        for attempt in range(self.config.redis_retry_attempts):
            try:
                # Add timeout to Redis operations
                if hasattr(self.redis, 'socket_timeout'):
                    original_timeout = self.redis.socket_timeout
                    self.redis.socket_timeout = self.config.redis_timeout

                result = self.circuit_breaker.call(func, *args, **kwargs)

                if attempt > 0:
                    logger.info(f"Redis {operation_name} succeeded on attempt {attempt + 1}")

                return result

            except Exception as e:
                last_exception = e

                if attempt < self.config.redis_retry_attempts - 1:
                    backoff = self.config.redis_retry_backoff * (2 ** attempt)
                    logger.warning(f"Redis {operation_name} failed (attempt {attempt + 1}), retrying in {backoff:.2f}s: {e}")
                    time.sleep(backoff)
                else:
                    logger.error(f"Redis {operation_name} failed after {self.config.redis_retry_attempts} attempts: {e}")

            finally:
                # Restore original timeout
                if hasattr(self.redis, 'socket_timeout'):
                    try:
                        self.redis.socket_timeout = original_timeout
                    except:
                        pass

        # All retries failed
        if self.config.fallback_to_local:
            logger.warning(f"Falling back to local lock for {operation_name}")
            return self._local_fallback(operation_name, *args, **kwargs)
        else:
            raise last_exception

    def _local_fallback(self, operation_name, *args, **kwargs):
        """Fallback to local threading locks (not distributed!)"""
        with self._local_lock:
            if operation_name == "set":
                key, value = args[0], args[1]
                nx = kwargs.get('nx', False)

                if nx and key in self._local_locks:
                    return False

                self._local_locks[key] = {
                    'value': value,
                    'timestamp': time.time(),
                    'ttl': kwargs.get('px', 30000) / 1000  # Convert ms to seconds
                }
                return True

            elif operation_name == "eval":
                # Simulate Lua script execution
                script, _ = args[0], args[1]  # num_keys not used in fallback
                key, owner_id = args[2], args[3]

                if key in self._local_locks:
                    lock_data = self._local_locks[key]

                    # Check TTL
                    if time.time() - lock_data['timestamp'] > lock_data['ttl']:
                        del self._local_locks[key]
                        return 0

                    if lock_data['value'] == owner_id:
                        if 'DEL' in script:
                            del self._local_locks[key]
                            return 1
                        elif 'PEXPIRE' in script:
                            # Extend TTL
                            extend_ms = args[4] if len(args) > 4 else 5000
                            lock_data['ttl'] = extend_ms / 1000
                            lock_data['timestamp'] = time.time()
                            return 1

                return 0

            elif operation_name == "incr":
                key = args[0]
                if key not in self._local_locks:
                    self._local_locks[key] = {'value': 0, 'timestamp': time.time(), 'ttl': 3600}

                self._local_locks[key]['value'] += 1
                return self._local_locks[key]['value']

            elif operation_name == "expire":
                key, seconds = args[0], args[1]
                if key in self._local_locks:
                    self._local_locks[key]['ttl'] = seconds
                    self._local_locks[key]['timestamp'] = time.time()
                return True

            else:
                # Default fallback
                logger.warning(f"No local fallback for operation: {operation_name}")
                return False

    def set(self, *args, **kwargs):
        return self._execute_with_retry("set", self.redis.set, *args, **kwargs)

    def eval(self, *args, **kwargs):
        return self._execute_with_retry("eval", self.redis.eval, *args, **kwargs)

    def incr(self, *args, **kwargs):
        return self._execute_with_retry("incr", self.redis.incr, *args, **kwargs)

    def expire(self, *args, **kwargs):
        return self._execute_with_retry("expire", self.redis.expire, *args, **kwargs)

    def exists(self, *args, **kwargs):
        return self._execute_with_retry("exists", self.redis.exists, *args, **kwargs)

    def get(self, *args, **kwargs):
        return self._execute_with_retry("get", self.redis.get, *args, **kwargs)

    def pttl(self, *args, **kwargs):
        return self._execute_with_retry("pttl", self.redis.pttl, *args, **kwargs)

