#!/usr/bin/env python
# -*- coding: utf-8 -*-
from loguru import logger

from app.config import config_app
from app.const import STATUS_ON, LIMIT_NUMBER_OF_CODEX, QUANTITY_CONFIG_KEY
from app.decorators import sqlalchemy_session
from app.extensions import kafka_producer, db
from app.inum import GetCodeType
from app.libs.apis.a6api import A6Api
from app.libs.apis.third_party_api.context_code_api import ContextCodeApi
from app.libs.base.utility import Utility
from app.libs.base.redis_utils import RedisUtility
from app.repositories import codex_repo


class ConsumerGenerateCode:
    @classmethod
    @sqlalchemy_session(db)
    def running(cls, value):
        try:
            logger.info("Generate Code Start...")
            logger.info(value)
            if RedisUtility.get_kafka_flag() == STATUS_ON:
                logger.info("Flag all off")
                cls.get_product(value)
            else:
                logger.info("Flag all on")
        except Exception as e:
            logger.exception(e)
        return True

    @classmethod
    def get_product(cls, products):
        if not products:
            return

        recall_data = []

        for product in products:
            product_id = product.get("product_id") or 0
            logger.info(product_id)
            flag = RedisUtility.get_product_flag(product_id)
            logger.info(
                "Flag product {product_id}: {flag}".format(
                    product_id=product_id, flag=flag
                )
            )
            if flag == STATUS_ON:
                continue

            if product.get('code_stock') and product.get('code_min') and  product.get('code_stock') > product.get('code_min'):
                logger.info(f'product {product_id} has enough code')
                continue

            code_used = product.get("code_used") or 0
            code_min = code_used
            code_stock = 0
            logger.info(f"used_code_quantity:{code_used}")

            if code_used == 0:
                product_info = A6Api.get_product_info(product_id)
                logger.info(product_info)
                if not product_info:
                    continue
                code_min = product_info.get("code_min")
                code_stock = product_info.get("code_stock")

            quantity_data = cls.get_quantity_needed(
                code_min,
                code_stock,
            )
            quantity_missing = quantity_data.get("quantity_missing")
            quantity_need_get = quantity_data.get("quantity_need_get")
            logger.info(
                "Quantity need get: {quantity}".format(quantity=quantity_need_get)
            )
            if quantity_need_get < 1 or not product_id:
                continue

            product_suppliers = cls.get_product_suppliers_by_product_id(product_id)
            if product_suppliers:
                quantity_gotten = cls.get_product_code(
                    product_id, quantity_need_get, product_suppliers
                )
                logger.info(f"Gotten {quantity_gotten}")
                logger.info(f"Missing {quantity_missing}")
                if 0 < quantity_gotten < quantity_missing:
                    data = dict(
                        product_id=product_id,
                        code_min=quantity_missing - quantity_gotten,
                        code_stock=0,
                        code_used=quantity_missing - quantity_gotten
                    )
                    recall_data.append(data)
        if len(recall_data) > 0:
            cls.get_missing_code(recall_data)

    @classmethod
    def get_product_suppliers_by_product_id(cls, product_id):
        return A6Api.get_suppliers(product_id) or None

    @classmethod
    def get_product_code(cls, product_id, quantity, product_suppliers):
        total_code = 0
        original_quantity = quantity
        for supplier in product_suppliers:

            if quantity < 1:
                return total_code

            if supplier["type"] != 1:
                continue

            behavior = ContextCodeApi.get_behavior_by_supplier(supplier)
            if behavior is None:
                continue
            context = ContextCodeApi(behavior)
            codes, response_data = context.get_code(
                dict(
                    product_id=supplier.get("product_id"),
                    supplier_id=supplier.get("supplier_id"),
                    product_supplier_id=supplier.get("id"),
                    code_prefix=supplier.get("code_prefix"),
                    code_length=supplier.get("code_length"),
                    product_code=supplier.get("product_code"),
                    quantity=quantity,
                    price=supplier.get("supplier_value"),
                    product_parent_id=supplier.get("product_parent_id"),
                    max_codes_per_call=supplier.get("max_codes_per_call"),
                    order_id=0,
                    retry_transaction_id=0,
                    supplier_order_id=0
                )
            )
            # logger.info(codes_res)
            # get_code_from_api = lambda result: result[0] if isinstance(result, tuple) else result
            # codes = get_code_from_api(codes_res)
            codexs = cls.save_code_to_database(
                product_id,
                supplier["supplier_id"],
                supplier["whoexport"],
                codes,
                supplier.get("product_parent_id"),
                supplier.get("debt_recognition"),
            )
            codex_data = {
                "po_code": "",
                "request_id": "",
                "transaction_id": response_data.get("transaction_id"),
                "type": GetCodeType.GET_CODE_FROM_API.value,
                "codex_items": [{
                    "codex_id": codex.id,
                    "partner_codex_id": ""
                } for codex in codexs]
            }
            kafka_producer.push(
                config_app.TOPIC_INSERT_CODEX_DATA,
                {
                    "type": "INSERT_CODEX_DATA",
                    "payload": codex_data
                },
            )


            gotten_quantity = len(codexs)
            logger.info(gotten_quantity)
            if gotten_quantity > 0:
                cls.update_quantity_stock(product_id, gotten_quantity)
                quantity -= gotten_quantity
                total_code += gotten_quantity
            if len(codes) == quantity:
                return total_code
        return total_code

    @classmethod
    def get_quantity_needed(cls, minimum_quantity, current_quantity):
        minimum_quantity = minimum_quantity or 0
        logger.info(minimum_quantity)
        current_quantity = current_quantity or 0
        logger.info(current_quantity)
        quantity_config = Utility.get_config(QUANTITY_CONFIG_KEY) or 0
        logger.info(quantity_config)

        quantity_missing = minimum_quantity - current_quantity + quantity_config
        logger.info(quantity_missing)

        if quantity_missing > LIMIT_NUMBER_OF_CODEX:
            quantity_need_get = LIMIT_NUMBER_OF_CODEX
        elif quantity_missing < 0:
            quantity_need_get = 0
        else:
            quantity_need_get = quantity_missing
        logger.info(quantity_need_get)
        return dict(
            quantity_missing=quantity_missing, quantity_need_get=quantity_need_get
        )

    @classmethod
    def save_code_to_database(
            cls, product_id, supplier_id, code_type, codes, product_parent_id, debt_recognition
    ):
        codexs = []
        for code in codes:
            codex = codex_repo.create(
                product_id=product_id,
                supplier_id=supplier_id,
                codex=code.get("codex"),
                codex_int=code.get("codex_int"),
                serial=code.get("serial"),
                pin=code.get("pin"),
                expired_time=code.get("expired"),
                code_type=code_type,
                product_parent_id=product_parent_id,
                debt_recognition=debt_recognition,
            )
            if codex is not None:
                codexs.append(codex)
        return codexs

    @classmethod
    def update_quantity_stock(cls, product_id, quantity):
        kafka_producer.push(
            config_app.TOPIC_CHANGE_QUANTITY,
            {
                "type": "ADDITION",
                "payload": [{"quantity": quantity, "product_id": product_id}],
            },
        )

    @classmethod
    def get_missing_code(cls, payload):
        kafka_producer.push(
            config_app.TOPIC_GENERATE_CODE,
            {
                "type": "GENERATE_CODE",
                "payload": payload,
            },
        )
